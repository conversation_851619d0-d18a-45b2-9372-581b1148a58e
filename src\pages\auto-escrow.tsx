import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ArrowLeft, Shield, Clock, CheckCircle, XCircle, Package, Monitor, Wrench, Mail, DollarSign, FileText, Star, TrendingUp, Users, Award } from 'lucide-react';

// Define type for view selection props
type ViewSelectorProps = {
  setActiveView: (view: string) => void;
}

// Main App Component
export default function App() {
  // State to manage which content view is active
  const [activeView, setActiveView] = useState('home');

  // A simple mapping of views to their content components
  const renderContent = () => {
    switch (activeView) {
      case 'create':
        return <CreateDealView />;
      case 'active':
        return <ActiveDealsView />;
      case 'archive':
        return <ArchiveDealsView />;
      case 'terms':
        return <TermsAndConditionsView />;
      default:
        return <HomeView setActiveView={setActiveView} />;
    }
  };

  return (
    <div className="min-h-screen font-sans text-slate-300 p-4 sm:p-6 lg:p-8" style={{ backgroundColor: '#101828' }}>
      <div className="container mx-auto max-w-7xl">
        {/* Main Content */}
        <main className="w-full">
          {activeView !== 'home' && (
            <Button
              onClick={() => setActiveView('home')}
              variant="ghost"
              className="mb-8 text-slate-400 hover:text-white transition-all duration-300 flex items-center gap-2 hover:bg-slate-800/50 p-3 rounded-lg group"
            >
              <ArrowLeft className="w-5 h-5 transition-transform group-hover:-translate-x-1" />
              Back to Home
            </Button>
          )}
          {renderContent()}
        </main>
      </div>
    </div>
  );
}

// Home View Component (The main screen from the image)
const HomeView = ({ setActiveView }: ViewSelectorProps) => (
  <div className="animate-fade-in">
    {/* Hero Section */}
    <div className="text-center py-16 lg:py-24">
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/20 via-blue-500/20 to-purple-500/20 blur-3xl opacity-30"></div>
        <div className="relative">
          <div className="flex items-center justify-center gap-3 mb-6">
            <Shield className="w-12 h-12 text-cyan-400" />
            <h1 className="text-5xl md:text-7xl font-bold tracking-wider uppercase bg-gradient-to-r from-white via-cyan-100 to-white bg-clip-text text-transparent">
              AUTO-ESCROW
            </h1>
          </div>
          <p className="text-xl md:text-2xl text-slate-300 font-light mb-4">
            Secure transactions made simple
          </p>
          <p className="text-slate-400 max-w-2xl mx-auto leading-relaxed">
            Experience the future of secure online transactions with our automated escrow service.
            Protect your deals with enterprise-grade security and instant settlements.
          </p>
        </div>
      </div>
    </div>

    {/* Stats Section */}
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16">
      <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
        <CardContent className="p-6 text-center">
          <TrendingUp className="w-8 h-8 text-green-400 mx-auto mb-3" />
          <div className="text-2xl font-bold text-white">$2.5M+</div>
          <div className="text-slate-400 text-sm">Secured in transactions</div>
        </CardContent>
      </Card>
      <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
        <CardContent className="p-6 text-center">
          <Users className="w-8 h-8 text-blue-400 mx-auto mb-3" />
          <div className="text-2xl font-bold text-white">10,000+</div>
          <div className="text-slate-400 text-sm">Happy customers</div>
        </CardContent>
      </Card>
      <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
        <CardContent className="p-6 text-center">
          <Award className="w-8 h-8 text-yellow-400 mx-auto mb-3" />
          <div className="text-2xl font-bold text-white">99.9%</div>
          <div className="text-slate-400 text-sm">Success rate</div>
        </CardContent>
      </Card>
    </div>

    {/* Main Action Cards */}
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-12">
      <Card
        className="bg-gradient-to-br from-cyan-500/10 to-blue-500/10 border-cyan-500/30 hover:border-cyan-400/50 transition-all duration-300 cursor-pointer group hover:scale-105 hover:shadow-xl hover:shadow-cyan-500/10"
        onClick={() => setActiveView('create')}
      >
        <CardHeader>
          <div className="flex items-center gap-3">
            <div className="p-3 bg-cyan-500/20 rounded-lg group-hover:bg-cyan-500/30 transition-colors">
              <DollarSign className="w-6 h-6 text-cyan-400" />
            </div>
            <div>
              <CardTitle className="text-white text-xl">Create a Deal</CardTitle>
              <CardDescription className="text-slate-300">Start a new secure transaction</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-slate-400 mb-4">
            Set up a new escrow deal with automatic fund release upon completion. Perfect for freelancers, buyers, and sellers.
          </p>
          <div className="flex items-center text-cyan-400 text-sm font-medium">
            Get started <ArrowLeft className="w-4 h-4 ml-2 rotate-180 group-hover:translate-x-1 transition-transform" />
          </div>
        </CardContent>
      </Card>

      <Card
        className="bg-gradient-to-br from-green-500/10 to-emerald-500/10 border-green-500/30 hover:border-green-400/50 transition-all duration-300 cursor-pointer group hover:scale-105 hover:shadow-xl hover:shadow-green-500/10"
        onClick={() => setActiveView('active')}
      >
        <CardHeader>
          <div className="flex items-center gap-3">
            <div className="p-3 bg-green-500/20 rounded-lg group-hover:bg-green-500/30 transition-colors">
              <Clock className="w-6 h-6 text-green-400" />
            </div>
            <div>
              <CardTitle className="text-white text-xl">Active Deals</CardTitle>
              <CardDescription className="text-slate-300">Monitor ongoing transactions</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-slate-400 mb-4">
            Track your current escrow deals, view status updates, and manage pending transactions in real-time.
          </p>
          <div className="flex items-center text-green-400 text-sm font-medium">
            View deals <ArrowLeft className="w-4 h-4 ml-2 rotate-180 group-hover:translate-x-1 transition-transform" />
          </div>
        </CardContent>
      </Card>

      <Card
        className="bg-gradient-to-br from-purple-500/10 to-violet-500/10 border-purple-500/30 hover:border-purple-400/50 transition-all duration-300 cursor-pointer group hover:scale-105 hover:shadow-xl hover:shadow-purple-500/10"
        onClick={() => setActiveView('archive')}
      >
        <CardHeader>
          <div className="flex items-center gap-3">
            <div className="p-3 bg-purple-500/20 rounded-lg group-hover:bg-purple-500/30 transition-colors">
              <FileText className="w-6 h-6 text-purple-400" />
            </div>
            <div>
              <CardTitle className="text-white text-xl">Archive</CardTitle>
              <CardDescription className="text-slate-300">Review completed deals</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-slate-400 mb-4">
            Access your transaction history, download receipts, and review completed or cancelled deals.
          </p>
          <div className="flex items-center text-purple-400 text-sm font-medium">
            Browse archive <ArrowLeft className="w-4 h-4 ml-2 rotate-180 group-hover:translate-x-1 transition-transform" />
          </div>
        </CardContent>
      </Card>
    </div>

    {/* Terms Link */}
    <div className="text-center">
      <Button
        onClick={() => setActiveView('terms')}
        variant="ghost"
        className="text-slate-400 hover:text-white border border-slate-700 hover:border-slate-500 transition-all duration-300"
      >
        <FileText className="w-4 h-4 mr-2" />
        Terms and Conditions
      </Button>
    </div>
  </div>
);

// Create Deal View Component
const CreateDealView = () => {
  const [dealType, setDealType] = useState('goods');
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [buyerEmail, setBuyerEmail] = useState('');
  const [sellerEmail, setSellerEmail] = useState('');

  return (
    <div className="text-white animate-fade-in max-w-5xl mx-auto">
      <div className="text-center mb-12">
        <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-white to-cyan-100 bg-clip-text text-transparent">
          Create a New Deal
        </h2>
        <p className="text-slate-400 text-lg">
          Set up a secure escrow transaction in just a few steps
        </p>
      </div>

      <Card className="bg-slate-800/50 backdrop-blur-sm border-slate-700">
        <CardContent className="p-8">
          <form className="space-y-8">
            {/* Deal Type Selection */}
            <div>
              <label className="block text-lg font-semibold text-white mb-4">Deal Type</label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {[
                  { value: 'goods', label: 'Physical Goods', icon: Package, color: 'blue' },
                  { value: 'digital', label: 'Digital Products', icon: Monitor, color: 'purple' },
                  { value: 'services', label: 'Services', icon: Wrench, color: 'green' }
                ].map((type) => {
                  const IconComponent = type.icon;
                  const isSelected = dealType === type.value;
                  const colorClasses = {
                    blue: isSelected ? 'border-blue-500 bg-blue-500/10 text-blue-400' : 'border-slate-600 hover:border-blue-500/50 text-slate-300',
                    purple: isSelected ? 'border-purple-500 bg-purple-500/10 text-purple-400' : 'border-slate-600 hover:border-purple-500/50 text-slate-300',
                    green: isSelected ? 'border-green-500 bg-green-500/10 text-green-400' : 'border-slate-600 hover:border-green-500/50 text-slate-300'
                  };

                  return (
                    <Card
                      key={type.value}
                      className={`cursor-pointer transition-all duration-300 hover:scale-105 ${colorClasses[type.color as keyof typeof colorClasses]}`}
                      onClick={() => setDealType(type.value)}
                    >
                      <CardContent className="p-6 text-center">
                        <IconComponent className="w-8 h-8 mx-auto mb-3" />
                        <div className="font-semibold">{type.label}</div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </div>

            {/* Amount */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-lg font-semibold text-white mb-3">Deal Amount (USD)</label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400" />
                  <Input
                    type="number"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    placeholder="0.00"
                    className="pl-10 bg-slate-700/50 border-slate-600 text-white placeholder-slate-400 h-12 text-lg"
                  />
                </div>
              </div>

              <div>
                <label className="block text-lg font-semibold text-white mb-3">Escrow Fee</label>
                <Card className="bg-slate-700/30 border-slate-600">
                  <CardContent className="p-4">
                    <div className="text-2xl font-bold text-green-400">
                      ${amount ? (parseFloat(amount) * 0.025).toFixed(2) : '0.00'}
                    </div>
                    <div className="text-slate-400 text-sm">2.5% service fee</div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Participants */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-lg font-semibold text-white mb-3">Buyer Email</label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400" />
                  <Input
                    type="email"
                    value={buyerEmail}
                    onChange={(e) => setBuyerEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    className="pl-10 bg-slate-700/50 border-slate-600 text-white placeholder-slate-400 h-12"
                  />
                </div>
              </div>

              <div>
                <label className="block text-lg font-semibold text-white mb-3">Seller Email</label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400" />
                  <Input
                    type="email"
                    value={sellerEmail}
                    onChange={(e) => setSellerEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    className="pl-10 bg-slate-700/50 border-slate-600 text-white placeholder-slate-400 h-12"
                  />
                </div>
              </div>
            </div>

            {/* Description */}
            <div>
              <label className="block text-lg font-semibold text-white mb-3">Deal Description</label>
              <textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Describe what is being bought/sold and any specific terms..."
                rows={4}
                className="w-full bg-slate-700/50 border border-slate-600 rounded-lg text-white placeholder-slate-400 py-3 px-4 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent resize-none"
              />
            </div>

            {/* Submit Button */}
            <div className="flex justify-center pt-6">
              <Button
                type="submit"
                className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white font-bold py-3 px-12 rounded-lg transition-all duration-300 ease-in-out transform hover:scale-105 shadow-lg shadow-cyan-500/20 h-12 text-lg"
              >
                <Shield className="w-5 h-5 mr-2" />
                Create Escrow Deal
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

// Active Deals View Component
const ActiveDealsView = () => {
  const activeDeals = [
    {
      id: 'ESC-001',
      amount: 250.00,
      type: 'Digital Products',
      buyer: '<EMAIL>',
      seller: '<EMAIL>',
      status: 'Pending Payment',
      created: '2023-11-15',
      description: 'Website design package'
    },
    {
      id: 'ESC-002',
      amount: 1200.00,
      type: 'Physical Goods',
      buyer: '<EMAIL>',
      seller: '<EMAIL>',
      status: 'Awaiting Delivery',
      created: '2023-11-12',
      description: 'Gaming laptop'
    },
    {
      id: 'ESC-003',
      amount: 75.00,
      type: 'Services',
      buyer: '<EMAIL>',
      seller: '<EMAIL>',
      status: 'In Progress',
      created: '2023-11-10',
      description: 'Logo design service'
    }
  ];

  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'Pending Payment':
        return {
          color: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
          icon: Clock,
          iconColor: 'text-yellow-400'
        };
      case 'Awaiting Delivery':
        return {
          color: 'bg-blue-500/20 text-blue-400 border-blue-500/30',
          icon: Package,
          iconColor: 'text-blue-400'
        };
      case 'In Progress':
        return {
          color: 'bg-green-500/20 text-green-400 border-green-500/30',
          icon: CheckCircle,
          iconColor: 'text-green-400'
        };
      default:
        return {
          color: 'bg-slate-500/20 text-slate-400 border-slate-500/30',
          icon: Clock,
          iconColor: 'text-slate-400'
        };
    }
  };

  return (
    <div className="text-white animate-fade-in">
      <div className="text-center mb-12">
        <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-white to-green-100 bg-clip-text text-transparent">
          Your Active Deals
        </h2>
        <p className="text-slate-400 text-lg">
          Monitor and manage your ongoing escrow transactions
        </p>
      </div>

      <div className="grid gap-6">
        {activeDeals.map((deal) => {
          const statusConfig = getStatusConfig(deal.status);
          const StatusIcon = statusConfig.icon;

          return (
            <Card key={deal.id} className="bg-slate-800/50 backdrop-blur-sm border-slate-700 hover:border-slate-600 transition-all duration-300 hover:scale-[1.02] hover:shadow-xl">
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
                  <div className="flex-1">
                    <div className="flex items-center gap-4 mb-4">
                      <h3 className="text-2xl font-bold text-white">Deal #{deal.id}</h3>
                      <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium border ${statusConfig.color}`}>
                        <StatusIcon className={`w-4 h-4 ${statusConfig.iconColor}`} />
                        {deal.status}
                      </div>
                    </div>
                    <p className="text-slate-300 mb-4 text-lg">{deal.description}</p>
                    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                      <div className="flex items-center gap-2 text-slate-400">
                        <Package className="w-4 h-4" />
                        <span>{deal.type}</span>
                      </div>
                      <div className="flex items-center gap-2 text-slate-400">
                        <Clock className="w-4 h-4" />
                        <span>{new Date(deal.created).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center gap-2 text-slate-400">
                        <Mail className="w-4 h-4" />
                        <span>{deal.buyer}</span>
                      </div>
                      <div className="flex items-center gap-2 text-slate-400">
                        <Mail className="w-4 h-4" />
                        <span>{deal.seller}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col lg:items-end gap-4">
                    <div className="text-3xl font-bold text-green-400">${deal.amount.toFixed(2)}</div>
                    <Button className="bg-cyan-500 hover:bg-cyan-600 text-white px-6 py-2 rounded-lg transition-colors">
                      View Details
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

// Archive Deals View Component
const ArchiveDealsView = () => {
  const archivedDeals = [
    {
      id: 'ESC-098',
      amount: 450.00,
      type: 'Digital Products',
      buyer: '<EMAIL>',
      seller: '<EMAIL>',
      status: 'Completed',
      completed: '2023-11-08',
      description: 'Mobile app development'
    },
    {
      id: 'ESC-097',
      amount: 180.00,
      type: 'Physical Goods',
      buyer: '<EMAIL>',
      seller: '<EMAIL>',
      status: 'Completed',
      completed: '2023-11-05',
      description: 'Vintage camera'
    },
    {
      id: 'ESC-096',
      amount: 320.00,
      type: 'Services',
      buyer: '<EMAIL>',
      seller: '<EMAIL>',
      status: 'Cancelled',
      completed: '2023-11-02',
      description: 'Content writing package'
    }
  ];

  const getArchiveStatusConfig = (status: string) => {
    switch (status) {
      case 'Completed':
        return {
          color: 'bg-green-500/20 text-green-400 border-green-500/30',
          icon: CheckCircle,
          iconColor: 'text-green-400'
        };
      case 'Cancelled':
        return {
          color: 'bg-red-500/20 text-red-400 border-red-500/30',
          icon: XCircle,
          iconColor: 'text-red-400'
        };
      default:
        return {
          color: 'bg-slate-500/20 text-slate-400 border-slate-500/30',
          icon: Clock,
          iconColor: 'text-slate-400'
        };
    }
  };

  return (
    <div className="text-white animate-fade-in">
      <div className="text-center mb-12">
        <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-white to-purple-100 bg-clip-text text-transparent">
          Archived Deals
        </h2>
        <p className="text-slate-400 text-lg">
          Review your completed and cancelled transactions
        </p>
      </div>

      <div className="grid gap-6">
        {archivedDeals.map((deal) => {
          const statusConfig = getArchiveStatusConfig(deal.status);
          const StatusIcon = statusConfig.icon;

          return (
            <Card key={deal.id} className="bg-slate-800/30 backdrop-blur-sm border-slate-700/50 hover:border-slate-600/50 transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
                  <div className="flex-1">
                    <div className="flex items-center gap-4 mb-4">
                      <h3 className="text-2xl font-bold text-slate-300">Deal #{deal.id}</h3>
                      <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium border ${statusConfig.color}`}>
                        <StatusIcon className={`w-4 h-4 ${statusConfig.iconColor}`} />
                        {deal.status}
                      </div>
                    </div>
                    <p className="text-slate-400 mb-4 text-lg">{deal.description}</p>
                    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                      <div className="flex items-center gap-2 text-slate-500">
                        <Package className="w-4 h-4" />
                        <span>{deal.type}</span>
                      </div>
                      <div className="flex items-center gap-2 text-slate-500">
                        <Clock className="w-4 h-4" />
                        <span>{new Date(deal.completed).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center gap-2 text-slate-500">
                        <Mail className="w-4 h-4" />
                        <span>{deal.buyer}</span>
                      </div>
                      <div className="flex items-center gap-2 text-slate-500">
                        <Mail className="w-4 h-4" />
                        <span>{deal.seller}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col lg:items-end gap-4">
                    <div className="text-3xl font-bold text-slate-400">${deal.amount.toFixed(2)}</div>
                    <Button variant="outline" className="border-slate-600 hover:border-slate-500 text-slate-300 hover:text-white px-6 py-2 rounded-lg transition-colors">
                      View Archive
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

// Terms and Conditions View Component
const TermsAndConditionsView = () => (
    <div className="text-left max-w-5xl mx-auto text-white animate-fade-in">
        <div className="text-center mb-12">
            <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
                Auto-Escrow Terms and Conditions
            </h2>
            <p className="text-slate-400 text-lg">
                Legal framework for secure escrow transactions
            </p>
        </div>

        <Card className="bg-slate-800/50 backdrop-blur-sm border-slate-700">
            <CardContent className="p-8">
                <div className="space-y-8 text-slate-300">
                    <Card className="bg-cyan-500/10 border-cyan-500/30">
                        <CardContent className="p-6">
                            <div className="flex items-center gap-3 mb-3">
                                <FileText className="w-6 h-6 text-cyan-400" />
                                <p className="text-cyan-400 font-semibold text-lg">
                                    Last Updated: November 15, 2023
                                </p>
                            </div>
                            <p className="text-slate-300">
                                Please read these terms carefully before using our Auto-Escrow service.
                            </p>
                        </CardContent>
                    </Card>

                    <section className="space-y-4">
                        <h3 className="text-2xl font-semibold text-white mb-4 flex items-center">
                            <div className="bg-gradient-to-r from-cyan-500 to-blue-500 text-white rounded-full w-10 h-10 flex items-center justify-center text-sm font-bold mr-4">1</div>
                            Introduction
                        </h3>
                        <p className="mb-4 leading-relaxed">
                            Welcome to Auto-Escrow, a secure transaction platform operated by RMarket. These terms and conditions ("Terms") outline the rules and regulations for the use of our escrow services. By using our service, you agree to be bound by these Terms.
                        </p>
                        <p className="leading-relaxed">
                            Our escrow service acts as a neutral third party to facilitate secure transactions between buyers and sellers, ensuring that funds are only released when all agreed-upon conditions are met.
                        </p>
                    </section>

                    <section className="space-y-4">
                        <h3 className="text-2xl font-semibold text-white mb-4 flex items-center">
                            <div className="bg-gradient-to-r from-cyan-500 to-blue-500 text-white rounded-full w-10 h-10 flex items-center justify-center text-sm font-bold mr-4">2</div>
                            Service Description
                        </h3>
                        <p className="mb-4 leading-relaxed">
                            Auto-Escrow provides a secure platform for holding funds in a transaction between two or more parties until specified conditions are met. Our services include:
                        </p>
                        <ul className="list-disc list-inside space-y-2 ml-6 mb-4 text-slate-300">
                            <li>Secure fund holding and management</li>
                            <li>Transaction monitoring and verification</li>
                            <li>Dispute resolution assistance</li>
                            <li>Automated release of funds upon completion</li>
                            <li>Transaction history and reporting</li>
                        </ul>
                    </section>

                <section>
                    <h3 className="text-2xl font-semibold text-white mb-4 flex items-center">
                        <span className="bg-cyan-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">3</span>
                        User Responsibilities
                    </h3>
                    <p className="mb-4">
                        Users are responsible for:
                    </p>
                    <ul className="list-disc list-inside space-y-2 ml-4 mb-4">
                        <li>Providing accurate and complete information</li>
                        <li>Complying with all applicable laws and regulations</li>
                        <li>Maintaining the confidentiality of account credentials</li>
                        <li>Promptly reporting any suspicious activity</li>
                        <li>Ensuring all transaction details are correct before confirmation</li>
                    </ul>
                </section>

                <section>
                    <h3 className="text-2xl font-semibold text-white mb-4 flex items-center">
                        <span className="bg-cyan-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">4</span>
                        Fees and Charges
                    </h3>
                    <p className="mb-4">
                        Our escrow service charges a fee of 2.5% of the transaction amount. This fee covers:
                    </p>
                    <ul className="list-disc list-inside space-y-2 ml-4 mb-4">
                        <li>Secure fund management and storage</li>
                        <li>Transaction processing and verification</li>
                        <li>Customer support and dispute resolution</li>
                        <li>Platform maintenance and security</li>
                    </ul>
                    <p>
                        Fees are automatically deducted from the transaction amount before release to the seller.
                    </p>
                </section>

                <section>
                    <h3 className="text-2xl font-semibold text-white mb-4 flex items-center">
                        <span className="bg-cyan-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">5</span>
                        Dispute Resolution
                    </h3>
                    <p className="mb-4">
                        In case of disputes between parties, Auto-Escrow will:
                    </p>
                    <ul className="list-disc list-inside space-y-2 ml-4 mb-4">
                        <li>Review all available evidence and documentation</li>
                        <li>Communicate with both parties to understand the issue</li>
                        <li>Make a fair determination based on the evidence</li>
                        <li>Release funds according to the resolution decision</li>
                    </ul>
                </section>

                <section>
                    <h3 className="text-2xl font-semibold text-white mb-4 flex items-center">
                        <span className="bg-cyan-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">6</span>
                        Limitation of Liability
                    </h3>
                    <p className="mb-4">
                        Auto-Escrow's liability is limited to the amount of funds held in escrow for the specific transaction. We are not liable for:
                    </p>
                    <ul className="list-disc list-inside space-y-2 ml-4 mb-4">
                        <li>Indirect, incidental, or consequential damages</li>
                        <li>Loss of profits or business opportunities</li>
                        <li>Actions or omissions of transaction parties</li>
                        <li>Technical issues beyond our reasonable control</li>
                    </ul>
                </section>

                <section>
                    <h3 className="text-2xl font-semibold text-white mb-4 flex items-center">
                        <span className="bg-cyan-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">7</span>
                        Privacy and Security
                    </h3>
                    <p className="mb-4">
                        We are committed to protecting your privacy and maintaining the security of your transactions. All personal and financial information is encrypted and stored securely. We do not share your information with third parties except as required by law or for transaction processing.
                    </p>
                </section>

                <section>
                    <h3 className="text-2xl font-semibold text-white mb-4 flex items-center">
                        <span className="bg-cyan-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">8</span>
                        Termination
                    </h3>
                    <p className="mb-4">
                        We reserve the right to terminate or suspend access to our services for violations of these Terms or for any other reason at our sole discretion. Upon termination, any funds held in escrow will be handled according to the status of ongoing transactions.
                    </p>
                </section>

                    <Card className="bg-slate-700/50 border-slate-600">
                        <CardContent className="p-6">
                            <h4 className="text-xl font-semibold text-white mb-4 flex items-center">
                                <Mail className="w-6 h-6 mr-3 text-cyan-400" />
                                Contact Information
                            </h4>
                            <p className="text-slate-300 mb-4">
                                If you have any questions about these Terms, please contact our support team at:
                            </p>
                            <div className="space-y-3">
                                <div className="flex items-center gap-3">
                                    <Mail className="w-5 h-5 text-cyan-400" />
                                    <span><EMAIL></span>
                                </div>
                                <div className="flex items-center gap-3">
                                    <Clock className="w-5 h-5 text-cyan-400" />
                                    <span>+1 (555) 123-ESCROW</span>
                                </div>
                                <div className="flex items-center gap-3">
                                    <Clock className="w-5 h-5 text-cyan-400" />
                                    <span>Monday - Friday, 9:00 AM - 6:00 PM EST</span>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </CardContent>
        </Card>
    </div>
);
